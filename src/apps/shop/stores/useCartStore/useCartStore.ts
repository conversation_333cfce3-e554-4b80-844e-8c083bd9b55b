import { enableMapSet } from 'immer';
import { apiErrorNotification, createStore } from '@/utils';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { CartItemType, CartType } from '@/libs/cart/types';
import { fetchApi } from '@/libs/utils/api';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { ApiErrorProps } from '@/types/utility';
import type { CheckoutResponseType } from './type';
import { OfferType } from '@/types';

enableMapSet();

type State = CartType & {
  isCartLoading: boolean;
  errorOnCartLoading: boolean;
  offersMapData: Record<
    string,
    {
      quantity: number;
      isLoading: boolean;
    }
  >;
};
type Actions = {
  fetchCart: () => Promise<void>;
  clearCart: VoidFunction;
  setOfferMapData: (
    productOfferId: string,
    offerData: Partial<State['offersMapData']['string']>,
  ) => void;
  swapOfferCartItem: (item: CartItemType, newOfferId: string) => void;
  checkout: () => Promise<CheckoutResponseType>;
  addToCart: (params: {
    offers: {
      productOfferId: string;
      quantity: number;
    }[];
    onError: (message: string) => void;
  }) => void;
};

export const INITIAL_STATE: State = {
  budget: null,
  vendors: [],
  isCartLoading: false,
  errorOnCartLoading: false,
  itemsCount: 0,
  uniqueItemsCount: 0,
  subtotal: '0',
  total: '0',
  offersMapData: {},
};

export const useCartStore = createStore<Actions & State>()(
  immer(
    devtools((set, getState) => ({
      ...INITIAL_STATE,
      fetchCart: async () => {
        set({ isCartLoading: true, errorOnCartLoading: false });

        try {
          const response = await fetchApi<CartType>('/cart');

          set({ ...response!, offersMapData: {} });

          response.vendors
            .flatMap((vendor) => vendor.items)
            .forEach((item) => {
              getState().setOfferMapData(item.productOfferId, {
                quantity: item.quantity,
                isLoading: false,
              });
            });
        } catch (error) {
          // TODO: Handle it better
          set({ errorOnCartLoading: true });
          console.error(error);
        }

        set({ isCartLoading: false });
      },
      clearCart: async () => {
        set({ isCartLoading: false });

        const response = await fetchApi<CartType>('/cart', {
          method: 'DELETE',
        });

        set(response);
      },
      setOfferMapData: (
        productOfferId: string,
        offerData: Partial<State['offersMapData']['string']>,
      ) => {
        const { offersMapData } = getState();

        const newOffersMapData = {
          ...offersMapData,
          ...{
            [productOfferId]: {
              ...(offersMapData[productOfferId] ?? {
                quantity: 0,
                isLoading: false,
              }),
              ...offerData,
            },
          },
        };

        set({
          offersMapData: newOffersMapData,
        });
      },
      addToCart: async ({ offers, onError }) => {
        const state = getState();

        const offersToUpdate = offers.filter((offer) => {
          const currentItem = state.vendors
            .flatMap((vendor) => vendor.items)
            .find((item) => item.productOfferId === offer.productOfferId);

          return !currentItem || currentItem.quantity !== offer.quantity;
        });

        if (offersToUpdate.length === 0) {
          return;
        }

        const { items, productOfferIds } = offersToUpdate.reduce(
          (acc, { productOfferId, quantity }) => {
            acc.items.push({ productOfferId, quantity, notes: '' });
            acc.productOfferIds.push(productOfferId);
            return acc;
          },
          {
            items: [] as {
              productOfferId: string;
              quantity: number;
              notes: string;
            }[],
            productOfferIds: [] as string[],
          },
        );

        try {
          items.forEach((item) => {
            getState().setOfferMapData(item.productOfferId, {
              isLoading: true,
              quantity: item.quantity,
            });
          });
          set({ isCartLoading: true });
          const response = await fetchApi<{
            subtotal: string;
            itemsCount: number;
            uniqueItemsCount: number;
          }>('/cart/cart-items', {
            method: 'POST',
            body: { items },
          });

          set((state) => {
            state.subtotal = response.subtotal;
            state.itemsCount = response.itemsCount;
            state.uniqueItemsCount = response.uniqueItemsCount;
          });

          getState().fetchCart();
        } catch (err) {
          const { data } = err as ApiErrorProps;
          apiErrorNotification(data.message);
          onError(data?.message ?? '');
        } finally {
          productOfferIds.forEach((productOfferId) => {
            getState().setOfferMapData(productOfferId, { isLoading: false });
          });
        }
      },
      checkout: async () => {
        const clinic = useClinicStore.getState().clinic;

        const data = await fetchApi<CheckoutResponseType>(
          `/clinics/${clinic?.id}/orders`,
          {
            method: 'POST',
            body: {
              isBillingSameAsShippingAddress: true,
              paymentMethod: 'INVOICE',
              shippingAddress: clinic?.shippingAddress,
              billingAddress: clinic?.billingAddress,
            },
          },
        );

        return data;
      },
      swapOfferCartItem: async (item, newOfferId) => {
        try {
          const currentOffer = item.product.offers.find(
            (offer) => offer.id === item.productOfferId,
          ) as OfferType;
          const newOffer = item.product.offers.find(
            (offer) => offer.id === newOfferId,
          ) as OfferType;

          let quantity = item.quantity;
          const currentIncrement = currentOffer.increments || 1;
          const newIncrement = newOffer.increments || 1;

          if (currentIncrement !== newIncrement) {
            quantity = newIncrement;
          }

          await fetchApi<CartType>(`/cart/cart-items/${item.id}`, {
            method: 'PATCH',
            body: {
              productOfferId: newOfferId,
              quantity,
            },
          });

          await getState().fetchCart();
        } catch (err) {
          const { data } = err as ApiErrorProps;

          apiErrorNotification(data.message);
        }
      },
    })),
  ),
);
