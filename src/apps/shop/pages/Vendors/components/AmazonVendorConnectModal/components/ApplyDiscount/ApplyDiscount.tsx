import { Checkbox } from '@/libs/form/Checkbox';
import { Button } from '@/libs/ui/Button/Button';
import { useState } from 'react';
import { AMAZON_CONNECT_CODE, AMAZON_DISCOUNT_URL } from '../../constants';
import { t } from 'i18next';
import { Icon } from '@/libs/icons/Icon';

const urls = [
  {
    label: t('client.vendors.amazon.existingAccount'),
    url: AMAZON_DISCOUNT_URL,
  },
  {
    label: t('client.vendors.amazon.noAccount'),
    url: AMAZON_DISCOUNT_URL,
  },
  {
    label: t('client.vendors.amazon.convertAccount'),
    url: AMAZON_DISCOUNT_URL,
  },
];

interface ApplyDiscountProps {
  onNext: () => void;
}

export const ApplyDiscount = ({ onNext }: ApplyDiscountProps) => {
  const [checked, setChecked] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(AMAZON_CONNECT_CODE);
    setIsCopied(!isCopied);
  };

  return (
    <div className="flex w-full flex-col">
      <p className="mb-1 text-sm">{t('client.vendors.amazon.yourCode')}</p>
      <Button
        className="mb-6 h-12 border border-black/[0.09] bg-[#F7F7F7]"
        onClick={copyToClipboard}
      >
        <span>{AMAZON_CONNECT_CODE}</span>
        <Icon name="copy" aria-hidden={true} className="mr-1 ml-auto" />
        <span className="font-medium">{isCopied ? 'Copied!' : 'Copy'}</span>
      </Button>
      <p className="mb-2 text-sm">{t('client.vendors.amazon.pasteCode')}</p>
      {urls.map((item) => (
        <Button
          key={item.label}
          className="mb-2 h-12"
          href={item.url}
          target="_blank"
        >
          {item.label}
        </Button>
      ))}
      <label className="mt-3 mb-5 flex items-center gap-2">
        <Checkbox
          checked={checked}
          onChange={(e) => setChecked(e.target.checked)}
        />
        <p className="m-0 text-xs">
          {t('client.vendors.amazon.discountApplied')}
        </p>
      </label>
      <Button className="mb-4 h-12" onClick={onNext} disabled={!checked}>
        <span className="font-semibold">
          {t('client.vendors.amazon.nextStep')}
        </span>
      </Button>
    </div>
  );
};
