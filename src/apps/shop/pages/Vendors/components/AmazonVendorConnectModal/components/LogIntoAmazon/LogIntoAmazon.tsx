import { useForm } from 'react-hook-form';
import * as Yup from 'yup';

import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import {
  type ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { VendorType } from '@/types';
import { yupResolver } from '@hookform/resolvers/yup';

import { useVendorsStore } from '@/apps/shop/stores/useVendorsStore/useVendorsStore';
import { Button } from '@/libs/ui/Button/Button';
import { Input } from '@/libs/form/Input';
import { t } from 'i18next';

type VendorConnectModalOptions = ModalOptionProps & {
  vendor: VendorType;
};

export interface AmazonVendorConnectForm {
  buyingGroupId: string;
}

const SCHEMA = Yup.object().shape({
  buyingGroupId: Yup.string()
    .trim()
    .required(
      t('form.errorMessage.required', {
        path: 'System User ',
      }),
    ),
});

interface LogIntoAmazonProps {
  buttonLabel: string;
  onPrevious: () => void;
}
export const LogIntoAmazon = ({
  buttonLabel,
  onPrevious,
}: LogIntoAmazonProps) => {
  const { modalOption } = useModalStore();
  const { connectAmazonVendor } = useVendorsStore();
  const { vendor } = modalOption as VendorConnectModalOptions;
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<AmazonVendorConnectForm>({
    resolver: yupResolver(SCHEMA),
  });

  const { apiRequest: handleConnect, isLoading } = useAsyncRequest({
    apiFunc: handleSubmit(async (values) => {
      const { redirectUri } = await connectAmazonVendor({
        vendorId: vendor.id,
        ...values,
      });

      window.location.href = redirectUri;
      // TODO: Handle error
      reset();
    }),
  });

  if (!vendor) {
    return null;
  }

  return (
    <>
      <form onSubmit={handleConnect} className="w-full">
        <div className="mb-4">
          <Input
            label="System User from Purchase System "
            {...register('buyingGroupId')}
            error={errors.buyingGroupId?.message}
          />
        </div>
        <Button className="mb-4" loading={isLoading}>
          {t('common.connect')}
        </Button>
      </form>
      <Button variant="unstyled" onClick={onPrevious}>
        <span className="underline">
          {t('client.vendors.amazon.previousStep')}
        </span>
      </Button>
    </>
  );
};
